use crate::{
    basic::config::types::Settings,
    orebs::api::{add_oren_api, done_oren_api, init_oren_api, start_oren_api, verify_token_api},
    oren::time::init_oren,
};
use log::{debug, error, info};

use colored::Colorize;
use std::{
    fs::File,
    io::{BufRead, BufReader, Seek, SeekFrom},
    sync::Arc,
};
use thiserror::Error;
use tokio::time::{sleep, Duration};

/// OREN 服务操作相关错误
#[derive(Error, Debug)]
pub enum OrenAddError {
    /// Token 验证错误
    #[error("Token 验证错误: {0}")]
    TokenVerificationError(String),

    /// 服务初始化错误
    #[error("服务初始化错误: {0}")]
    InitializationError(String),

    /// 日志监控错误
    #[error("日志监控错误: {0}")]
    LogMonitorError(String),

    /// 服务添加错误
    #[error("服务添加错误: {0}")]
    AddServiceError(String),

    /// 服务启动错误
    #[error("服务启动错误: {0}")]
    StartServiceError(String),

    /// 异步任务错误
    #[error("异步任务错误: {0}")]
    TaskJoinError(String),

    /// 服务完成错误
    #[error("服务完成错误: {0}")]
    CompleteServiceError(String),

    /// API 调用错误
    #[error("API 调用错误: {0}")]
    ApiError(String),

    /// IO 错误
    #[error("IO 错误: {0}")]
    IoError(#[from] std::io::Error),
}

/// 定义 Result 类型别名
pub type Result<T> = std::result::Result<T, OrenAddError>;

/// 验证 Token
pub async fn verify_token(settings: &Arc<Settings>) -> Result<()> {
    info!("开始验证 Token...");
    println!("{}", "❚ 正在验证 Token...".yellow().bold());

    match verify_token_api(settings).await {
        Ok(_) => {
            info!("验证成功!");
            println!("{}", "验证成功!".green());
            Ok(())
        }
        Err(e) => {
            let err_msg = format!("验证失败: {}", e);
            error!("{}", err_msg);
            println!("{}", "请检查配置文件中无效的 Token".red());
            Err(OrenAddError::TokenVerificationError(err_msg))
        }
    }
}

/// 初始化 OpenResty Edge Node
pub async fn init_oren_service(settings: &Arc<Settings>) -> Result<()> {
    info!("正在初始化...");
    println!("{}", "❚ 正在初始化...".yellow().bold());

    match init_oren_api(settings).await {
        Ok(_) => {
            info!("初始化成功!");
            println!("{}", "初始化成功!".green());

            // 初始化成功, 设置服务器时间
            init_oren().map_err(|e| {
                let err_msg = format!("初始化失败: {}", e);
                error!("{}", err_msg);
                OrenAddError::InitializationError(err_msg)
            })?;

            Ok(())
        }
        Err(e) => {
            let err_msg = format!("初始化失败: {}", e);
            error!("{}", err_msg);
            Err(OrenAddError::ApiError(err_msg))
        }
    }
}

/// 监听日志文件
pub async fn monitor_log_file() -> Result<()> {
    info!(
        "开始监听日志文件, 请前往控制台添加节点, 自动执行程序将等待已同步节点为 100% 后继续执行."
    );
    println!(
        "{}",
        "❚ 现在请前往控制台添加节点...(自动执行程序将等待已同步节点为 100% 后继续执行)"
            .bright_cyan()
            .bold()
            .blink()
            .italic()
    );

    let log_file_path = "/usr/local/oredge-node/logs/error.log";
    debug!("监听日志文件: {}", log_file_path);

    let mut file = File::open(log_file_path).map_err(|e| {
        let err_msg = format!("打开日志文件失败: {}", e);
        error!("{}", err_msg);
        OrenAddError::LogMonitorError(err_msg)
    })?;

    file.seek(SeekFrom::End(0)).map_err(|e| {
        let err_msg = format!("定位到日志文件末尾失败: {}", e);
        error!("{}", err_msg);
        OrenAddError::LogMonitorError(err_msg)
    })?;

    let mut reader = BufReader::new(file);

    loop {
        let mut line = String::new();
        match reader.read_line(&mut line) {
            Ok(0) => {
                // 如果没有新内容, 等待一段时间再继续读取
                debug!("等待新的日志内容...");
                sleep(Duration::from_secs(1)).await;
            }
            Ok(_) => {
                if line.contains("[lua] privileged.lua:1020: successfully binary upgraded OpenResty, context: ngx.timer") {
                    info!("检测到控制台成功添加节点的日志信息");
                    println!("{}", "控制台添加节点成功!".green());
                    return Ok(());
                }
            }
            Err(e) => {
                let err_msg = format!("读取日志文件失败: {}", e);
                error!("{}", err_msg);
                return Err(OrenAddError::LogMonitorError(err_msg));
            }
        }
    }
}

/// 添加 OpenResty Edge Node (使用 WebSocket 双向同步)
pub async fn add_oren_service(settings: &Arc<Settings>) -> Result<()> {
    info!("正在添加...");
    println!("{}", "❚ 正在添加...".yellow().bold());

    match add_oren_api(settings).await {
        Ok(_) => {
            // WebSocket API 已经处理了双向同步和本地时间设定
            info!("添加成功!");
            println!("{}", "添加成功!".green());
            Ok(())
        }
        Err(e) => {
            let err_msg = format!("添加失败: {}", e);
            error!("{}", err_msg);
            Err(OrenAddError::ApiError(err_msg))
        }
    }
}

/// 启动 OpenResty Edge Node 服务 (使用 WebSocket 双向同步)
pub async fn start_oren_service(settings: &Arc<Settings>) -> Result<()> {
    info!("正在启动服务...");
    println!("{}", "❚ 正在启动服务...".yellow().bold());

    match start_oren_api(settings).await {
        Ok(_) => {
            // WebSocket API 已经处理了双向同步和本地启动逻辑
            info!("启动成功!");
            println!("{}", "启动成功!".green());
            Ok(())
        }
        Err(e) => {
            let err_msg = format!("启动失败: {}", e);
            error!("{}", err_msg);
            Err(OrenAddError::StartServiceError(err_msg))
        }
    }
}

/// 完成 OpenResty Edge Node 配置
pub async fn done_oren_service(settings: &Arc<Settings>) -> Result<()> {
    info!("开始完成配置...");
    println!("{}", "❚ 正在完成配置...".yellow().bold());

    match done_oren_api(settings).await {
        Ok(_) => {
            info!("配置完成!");
            println!("{}", "配置完成!".green());
            Ok(())
        }
        Err(e) => {
            let err_msg = format!("完成配置失败: {}", e);
            error!("{}", err_msg);
            Err(OrenAddError::CompleteServiceError(err_msg))
        }
    }
}
