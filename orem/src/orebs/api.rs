use crate::basic::config::types::Settings;
use crate::services::ip::get_admin_ipv4;
use log::{debug, error, info};

use colored::Colorize;
use futures_util::{SinkExt, StreamExt};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use thiserror::Error;
use tokio::time::{sleep, timeout, Duration};
use tokio_tungstenite::{connect_async, tungstenite::Message};
use uuid::Uuid;

/// OREBS WebSocket API 错误类型
#[derive(Error, Debug)]
pub enum OrebsApiError {
    /// WebSocket 连接错误
    #[error("WebSocket 连接错误: {0}")]
    ConnectionError(String),

    /// WebSocket 通信错误
    #[error("WebSocket 通信错误: {0}")]
    WebSocketError(#[from] tokio_tungstenite::tungstenite::Error),

    /// 消息序列化错误
    #[error("消息序列化错误: {0}")]
    SerializationError(#[from] serde_json::Error),

    /// IP 地址错误
    #[error("IP 地址错误: {0}")]
    IpError(String),

    /// API 响应错误
    #[error("API 响应错误: {0}")]
    ResponseError(String),

    /// Token 缺失错误
    #[error("Token 缺失")]
    MissingTokenError,

    /// 超时错误
    #[error("操作超时: {0}")]
    TimeoutError(String),

    /// 连接断开错误
    #[error("连接已断开")]
    ConnectionClosedError,
}

/// 定义模块结果类型
pub type Result<T> = std::result::Result<T, OrebsApiError>;

/// 客户端请求消息结构
#[derive(Serialize, Debug)]
pub struct ClientRequest {
    /// 唯一请求ID
    pub id: String,
    /// 操作类型
    #[serde(rename = "type")]
    pub operation_type: String,
    /// 认证Token
    pub token: String,
    /// 年份 (仅用于确认消息)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub year: Option<i32>,
}

/// 服务端响应消息结构
#[derive(Deserialize, Debug)]
pub struct ServerResponse {
    /// 对应的请求ID
    pub id: String,
    /// 响应代码, 0 表示成功
    pub code: i32,
    /// 响应消息
    pub msg: String,
    /// 操作类型
    #[serde(rename = "type")]
    pub operation_type: String,
}

/// 系统通知消息结构
#[derive(Deserialize, Debug)]
pub struct SystemNotification {
    /// 通知类型
    #[serde(rename = "type")]
    pub notification_type: String,
    /// 通知内容
    pub msg: String,
    /// 可选的额外数据
    #[serde(default)]
    pub data: Option<serde_json::Value>,
}

/// 打印错误信息到控制台
fn print_error(message: &str) {
    error!("WebSocket API 错误: {}", message);
    eprintln!("{}", message.red());
}

/// 获取 WebSocket URL
fn get_websocket_url(admin_ipv4: &str) -> Result<String> {
    Ok(format!("ws://{}:8056/ws", admin_ipv4))
}

/// 生成唯一请求ID
fn generate_request_id() -> String {
    Uuid::new_v4().to_string()
}

/// 创建客户端请求消息
fn create_request(operation_type: &str, token: &str) -> ClientRequest {
    ClientRequest {
        id: generate_request_id(),
        operation_type: operation_type.to_string(),
        token: format!("Bearer {}", token),
        year: None,
    }
}

/// 创建客户端确认消息
fn create_ack_request(operation_type: &str, year: Option<i32>) -> ClientRequest {
    ClientRequest {
        id: generate_request_id(),
        operation_type: operation_type.to_string(),
        token: String::new(), // 确认消息不需要Token
        year,
    }
}

/// WebSocket 客户端结构
pub struct WebSocketClient {
    settings: Arc<Settings>,
}

impl WebSocketClient {
    /// 创建新的 WebSocket 客户端
    pub fn new(settings: Arc<Settings>) -> Self {
        Self { settings }
    }

    /// 建立 WebSocket 连接
    async fn connect(
        &self,
    ) -> Result<(
        tokio_tungstenite::WebSocketStream<
            tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
        >,
        tokio_tungstenite::tungstenite::http::Response<Option<Vec<u8>>>,
    )> {
        // 获取管理节点 IP
        let admin_ipv4 = get_admin_ipv4(self.settings.clone())
            .map_err(|e| OrebsApiError::IpError(format!("获取管理节点 IPv4 失败: {}", e)))?;

        // 构建 WebSocket URL
        let ws_url = get_websocket_url(&admin_ipv4)?;
        debug!("连接 WebSocket URL: {}", ws_url);

        // 建立连接
        let (ws_stream, response) = connect_async(&ws_url)
            .await
            .map_err(|e| OrebsApiError::ConnectionError(format!("WebSocket 连接失败: {}", e)))?;

        info!("WebSocket 连接已建立");
        Ok((ws_stream, response))
    }

    /// 发送请求并等待响应
    async fn send_request_and_wait(&self, operation_type: &str) -> Result<ServerResponse> {
        // 获取 Token
        let token = self
            .settings
            .token
            .as_ref()
            .ok_or(OrebsApiError::MissingTokenError)?;

        // 建立连接
        let (mut ws_stream, _) = self.connect().await?;

        // 创建请求消息
        let request = create_request(operation_type, token);
        let request_id = request.id.clone();
        let request_json = serde_json::to_string(&request)?;

        // 发送请求
        debug!("发送 {} 请求: {}", operation_type, request_json);
        ws_stream.send(Message::Text(request_json.into())).await?;

        // 等待响应 (30秒超时)
        let response = timeout(Duration::from_secs(30), async {
            while let Some(msg) = ws_stream.next().await {
                match msg? {
                    Message::Text(text) => {
                        debug!("收到消息: {}", text);

                        // 尝试解析为服务端响应
                        if let Ok(server_response) = serde_json::from_str::<ServerResponse>(&text) {
                            if server_response.id == request_id {
                                return Ok(server_response);
                            }
                        }

                        // 尝试解析为系统通知
                        if let Ok(notification) = serde_json::from_str::<SystemNotification>(&text)
                        {
                            info!(
                                "收到系统通知 [{}]: {}",
                                notification.notification_type, notification.msg
                            );
                        }
                    }
                    Message::Close(_) => {
                        return Err(OrebsApiError::ConnectionClosedError);
                    }
                    _ => {}
                }
            }
            Err(OrebsApiError::ConnectionClosedError)
        })
        .await
        .map_err(|_| OrebsApiError::TimeoutError(format!("{} 操作超时", operation_type)))??;

        // 关闭连接
        ws_stream.close(None).await.ok();

        Ok(response)
    }

    /// 处理 add_oren 操作 (带双向同步)
    pub async fn handle_add_oren(&self) -> Result<()> {
        // 获取 Token
        let token = self
            .settings
            .token
            .as_ref()
            .ok_or(OrebsApiError::MissingTokenError)?;

        // 建立连接
        let (mut ws_stream, _) = self.connect().await?;

        // 创建请求消息
        let request = create_request("add_oren", token);
        let request_id = request.id.clone();
        let request_json = serde_json::to_string(&request)?;

        // 发送请求
        info!("开始添加...");
        debug!("发送 add_oren 请求: {}", request_json);
        ws_stream.send(Message::Text(request_json.into())).await?;

        // 处理消息循环
        while let Some(msg) = ws_stream.next().await {
            match msg? {
                Message::Text(text) => {
                    debug!("收到消息: {}", text);

                    // 尝试解析为服务端响应
                    if let Ok(server_response) = serde_json::from_str::<ServerResponse>(&text) {
                        if server_response.id == request_id {
                            if server_response.code == 0 {
                                info!("add_oren 操作完成: {}", server_response.msg);
                                break;
                            } else {
                                error!("add_oren 操作失败: {}", server_response.msg);
                                return Err(OrebsApiError::ResponseError(server_response.msg));
                            }
                        }
                    }

                    // 尝试解析为系统通知
                    if let Ok(notification) = serde_json::from_str::<SystemNotification>(&text) {
                        match notification.notification_type.as_str() {
                            "add_oren_start" | "add_oren_calculated" | "add_oren_year_start" => {
                                info!("进度通知: {}", notification.msg);
                            }
                            "add_oren_year_complete" => {
                                info!("年份处理完成: {}", notification.msg);

                                // 提取年份信息
                                if let Some(data) = &notification.data {
                                    if let Some(year) = data.get("year").and_then(|v| v.as_i64()) {
                                        // 发送确认消息
                                        let ack_request = create_ack_request(
                                            "add_oren_year_ack",
                                            Some(year as i32),
                                        );
                                        let ack_json = serde_json::to_string(&ack_request)?;
                                        debug!("发送年份确认: {}", ack_json);
                                        ws_stream.send(Message::Text(ack_json)).await?;
                                    }
                                }
                            }
                            "add_oren_error" => {
                                error!("add_oren 错误: {}", notification.msg);
                                return Err(OrebsApiError::ResponseError(notification.msg));
                            }
                            _ => {
                                info!(
                                    "收到系统通知 [{}]: {}",
                                    notification.notification_type, notification.msg
                                );
                            }
                        }
                    }
                }
                Message::Close(_) => {
                    return Err(OrebsApiError::ConnectionClosedError);
                }
                _ => {}
            }
        }

        // 关闭连接
        ws_stream.close(None).await.ok();
        Ok(())
    }

    /// 处理 start_oren 操作 (带双向同步)
    pub async fn handle_start_oren(&self) -> Result<()> {
        // 获取 Token
        let token = self
            .settings
            .token
            .as_ref()
            .ok_or(OrebsApiError::MissingTokenError)?;

        // 建立连接
        let (mut ws_stream, _) = self.connect().await?;

        // 创建请求消息
        let request = create_request("start_oren", token);
        let request_id = request.id.clone();
        let request_json = serde_json::to_string(&request)?;

        // 发送请求
        info!("开始启动服务...");
        debug!("发送 start_oren 请求: {}", request_json);
        ws_stream.send(Message::Text(request_json)).await?;

        let mut received_initial_response = false;

        // 处理消息循环
        while let Some(msg) = ws_stream.next().await {
            match msg? {
                Message::Text(text) => {
                    debug!("收到消息: {}", text);

                    // 尝试解析为服务端响应
                    if let Ok(server_response) = serde_json::from_str::<ServerResponse>(&text) {
                        if server_response.id == request_id {
                            if !received_initial_response {
                                // 第一次响应，表示服务端开始执行
                                if server_response.code == 0 {
                                    info!("服务端开始执行启动流程: {}", server_response.msg);
                                    received_initial_response = true;

                                    // 等待一段时间后发送确认
                                    sleep(Duration::from_secs(2)).await;
                                    let ack_request = create_ack_request("start_oren_ack", None);
                                    let ack_json = serde_json::to_string(&ack_request)?;
                                    debug!("发送启动确认: {}", ack_json);
                                    ws_stream.send(Message::Text(ack_json)).await?;
                                } else {
                                    error!("start_oren 操作失败: {}", server_response.msg);
                                    return Err(OrebsApiError::ResponseError(server_response.msg));
                                }
                            } else {
                                // 最终响应
                                if server_response.code == 0 {
                                    info!("start_oren 操作完成: {}", server_response.msg);
                                    break;
                                } else {
                                    error!("start_oren 操作失败: {}", server_response.msg);
                                    return Err(OrebsApiError::ResponseError(server_response.msg));
                                }
                            }
                        }
                    }

                    // 尝试解析为系统通知
                    if let Ok(notification) = serde_json::from_str::<SystemNotification>(&text) {
                        info!(
                            "收到系统通知 [{}]: {}",
                            notification.notification_type, notification.msg
                        );
                    }
                }
                Message::Close(_) => {
                    return Err(OrebsApiError::ConnectionClosedError);
                }
                _ => {}
            }
        }

        // 关闭连接
        ws_stream.close(None).await.ok();
        Ok(())
    }
}

/// 处理服务端响应
fn handle_server_response(response: ServerResponse) -> Result<()> {
    if response.code == 0 {
        debug!("WebSocket API 响应处理成功");
        Ok(())
    } else {
        error!("WebSocket API 响应处理失败: {}", response.msg);
        print_error(&response.msg);
        Err(OrebsApiError::ResponseError(response.msg))
    }
}



/// 初始化 OpenResty Edge Node
pub async fn init_oren_api(settings: &Arc<Settings>) -> Result<()> {
    info!("开始初始化...");
    let client = WebSocketClient::new(settings.clone());
    let response = client.send_request_and_wait("init_oren").await?;
    handle_server_response(response)
}

/// 添加 OpenResty Edge Node (带双向同步)
pub async fn add_oren_api(settings: &Arc<Settings>) -> Result<()> {
    let client = WebSocketClient::new(settings.clone());
    client.handle_add_oren().await
}

/// 启动 OpenResty Edge Node (带双向同步)
pub async fn start_oren_api(settings: &Arc<Settings>) -> Result<()> {
    let client = WebSocketClient::new(settings.clone());
    client.handle_start_oren().await
}

/// 完成 OpenResty Edge Node 配置
pub async fn done_oren_api(settings: &Arc<Settings>) -> Result<()> {
    info!("开始完成配置...");
    let client = WebSocketClient::new(settings.clone());
    let response = client.send_request_and_wait("done_oren").await?;
    handle_server_response(response)
}
